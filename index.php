<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON></title>
    <link href="../dist/output.css" rel="stylesheet">
    <link rel="icon" type="image/x-icon" href="./favicon.ico">

    <!-- Primary Meta Tags -->
    <meta name="title" content="<PERSON> · <PERSON>.com">
    <meta name="description" content="<PERSON> is a writer and entrepreneur who is passionate about helping people achieve their goals and dreams.">

    <!-- Facebook -->
    <meta property="og:type" content="book">
    <meta property="og:url" content="https://simmermann.com/">
    <meta property="og:title" content="<PERSON> · <PERSON>.com">
    <meta property="og:description" content="<PERSON> is a writer and entrepreneur who is passionate about helping people achieve their goals and dreams.">
    <meta property="og:image" content="#">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<PERSON> · <PERSON>.com">
    <meta name="twitter:description" content="<PERSON>mmer<PERSON> is a writer and entrepreneur who is passionate about helping people achieve their goals and dreams.">
    <meta name="twitter:image" content="#">

    <!-- Schema.org Structured Data -->
    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "Person",
            "name": "Martin Simmermann",
            "author": {
                "@type": "Person",
                "name": "Martin Simmermann"
            },
            "inLanguage": "en",
            "offers": {
                "@type": "Offer",
                "url": "https://a.co/d/jfUNiH5",
                "price": "2.99",
                "priceCurrency": "USD",
                "availability": "https://schema.org/InStock"
            },
            "description": "Martin Simmermann is a writer and entrepreneur who is passionate about helping people achieve their goals and dreams.",
            "image": "#"
        }
    </script>
</head>

<style>
    .text-animation-container {
        position: relative;
        display: inline-block;
        vertical-align: top;
        min-height: 1em;
        opacity: 0;
        transition: opacity 0.5s ease;
        white-space: nowrap;
    }

    .text-animation-container.loaded {
        opacity: 1;
    }

    .animated-text {
        position: absolute;
        left: 0;
        top: 0;
        transition: transform 2s ease-in-out, opacity 2s ease-in-out;
        white-space: nowrap;
        will-change: transform, opacity;
    }

    .animate-out {
        opacity: 0;
        transform: translateY(-10px);
    }

    .animate-in {
        opacity: 1;
        transform: translateY(0);
    }

    .hidden-below {
        opacity: 0;
        transform: translateY(10px);
    }
</style>
</head>

<body class="">

    <!-- Hero -->
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 pt-10">
        <!-- Grid -->
        <div class="grid md:grid-cols-2 gap-4 md:gap-8 xl:gap-20 md:items-center">
            <div class="text-center">
                <h1 class="block text-4xl font-semibold text-gray-700 sm:text-4xl lg:text-6xl lg:leading-tight">Martin is <span class="text-animation-container">
                        <span id="textA" class="animated-text">reading 📖</span>
                        <span id="textB" class="animated-text hidden-below"></span>
                    </span></h1>
            </div>
            <!-- End Col -->
        </div>
        <!-- End Grid -->
    </div>
    <!-- End Hero -->

    <!-- ========== MAIN CONTENT ========== -->
    <main id="content">
        <div class="max-w-6xl px-4 py-10 sm:px-6 lg:px-8 lg:py-14 mx-auto">
            <div class="mb-6 max-w-2xl text-center mx-auto pb-3">
                <h1 class="font-medium text-black text-3xl sm:text-4xl dark:text-white">
                    E-books
                </h1>
            </div>

            <!-- SECTION - My books -->
            <div class="grid sm:grid-cols-2 lg:grid-cols-3 gap-4">

                <!-- LINK - #3 The Farmer and the Unicorn -->
                <a href="./books/the-farmer-and-the-unicorn.php" rel="noopener noreferrer"
                    class="flex flex-col h-full transition duration-300 group border border-gray-200 rounded-xl"
                    onmouseover="this.style.boxShadow='0 8px 30px rgba(0,0,0,0.12)'; this.querySelector('h3').style.color='#3b82f6';"
                    onmouseout="this.style.boxShadow=''; this.querySelector('h3').style.color='';">
                    <!-- Cover -->
                    <div class="relative">
                        <img
                            class="w-full h-auto object-contain rounded-xl mx-auto"
                            src="/files/images/mybooks/the-farmer-and-the-unicorn.webp"
                            alt="The Farmer and the Unicorn" />
                    </div>
                    <!-- Content -->
                    <div class="flex flex-col h-full p-5">
                        <div class="text-center">
                            <!-- Title -->
                            <h3 class="text-2xl font-semibold text-gray-800 dark:text-neutral-300 transition-colors duration-300">
                                The Farmer and the Unicorn
                            </h3>
                        </div>

                        <!-- Description -->
                        <div class="mt-3 text-left text-gray-600 dark:text-neutral-400 text-md">
                            A farmer's life changes unexpectedly, leading him on a journey that teaches what truly matters in life.
                        </div>

                        <!-- CTA -->
                        <div class="mt-3 flex justify-center">
                            <p class="inline-flex items-center gap-x-1 text-md font-semibold text-gray-600 group-hover:text-blue-500">
                                Read more
                                <svg class="mt-1 shrink-0 size-4 transition ease-in-out group-hover:text-blue-500 group-hover:translate-x-1 group-focus:translate-x-1"
                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path d="m9 18 6-6-6-6" />
                                </svg>
                            </p>
                        </div>
                    </div>
                </a>

                <!-- LINK - #2 The Man Who Lost Everything -->
                <a href="./books/the-man-who-lost-everything.php" rel="noopener noreferrer"
                    class="flex flex-col h-full transition duration-300 group border border-gray-200 rounded-xl"
                    onmouseover="this.style.boxShadow='0 8px 30px rgba(0,0,0,0.12)'; this.querySelector('h3').style.color='#3b82f6';"
                    onmouseout="this.style.boxShadow=''; this.querySelector('h3').style.color='';">
                    <!-- Cover -->
                    <div class="relative">
                        <img
                            class="w-full h-auto object-contain rounded-xl mx-auto"
                            src="/files/images/mybooks/the-man-who-lost-everything.webp"
                            alt="The Man Who Lost Everything" />
                    </div>
                    <!-- Content -->
                    <div class="flex flex-col h-full p-5">
                        <div class="text-center">
                            <!-- Title -->
                            <h3 class="text-2xl font-semibold text-gray-800 dark:text-neutral-300 transition-colors duration-300">
                                The Man Who Lost Everything
                            </h3>
                        </div>

                        <!-- Description -->
                        <div class="mt-3 text-left text-gray-600 dark:text-neutral-400 text-md">
                            A heartfelt story of a man who only discovered the true beauty of his life after losing everything he once took for granted.
                        </div>

                        <!-- CTA -->
                        <div class="mt-3 flex justify-center">
                            <p class="inline-flex items-center gap-x-1 text-md font-semibold text-gray-600 group-hover:text-blue-500">
                                Read more
                                <svg class="mt-1 shrink-0 size-4 transition ease-in-out group-hover:text-blue-500 group-hover:translate-x-1 group-focus:translate-x-1"
                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path d="m9 18 6-6-6-6" />
                                </svg>
                            </p>
                        </div>
                    </div>
                </a>

                <!-- LINK - #1 Alphabet Heroes -->
                <a href="./books/alphabet-heroes.php" rel="noopener noreferrer"
                    class="flex flex-col h-full transition duration-300 group border border-gray-200 rounded-xl"
                    onmouseover="this.style.boxShadow='0 8px 30px rgba(0,0,0,0.12)'; this.querySelector('h3').style.color='#3b82f6';"
                    onmouseout="this.style.boxShadow=''; this.querySelector('h3').style.color='';">
                    <!-- Cover -->
                    <div class="relative">
                        <img
                            class="w-full h-auto object-contain rounded-xl mx-auto"
                            src="/files/images/mybooks/alphabet-heroes.webp"
                            alt="Alphabet Heroes" />
                    </div>
                    <!-- Content -->
                    <div class="flex flex-col h-full p-5">
                        <div class="text-center">
                            <!-- Title -->
                            <h3 class="text-2xl font-semibold text-gray-800 dark:text-neutral-300 transition-colors duration-300">
                                Alphabet Heroes
                            </h3>
                        </div>

                        <!-- Description -->
                        <div class="mt-3 text-left text-gray-600 dark:text-neutral-400 text-md">
                            Collection of bedtime stories designed to teach important life lessons and values through charming characters and their enchanting adventures.
                        </div>

                        <!-- CTA -->
                        <div class="mt-3 flex justify-center">
                            <p class="inline-flex items-center gap-x-1 text-md font-semibold text-gray-600 group-hover:text-blue-500">
                                Read more
                                <svg class="mt-1 shrink-0 size-4 transition ease-in-out group-hover:text-blue-500 group-hover:translate-x-1 group-focus:translate-x-1"
                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path d="m9 18 6-6-6-6" />
                                </svg>
                            </p>
                        </div>
                    </div>
                </a>

            </div>
            <!-- !SECTION - My books -->
        </div>

        <!-- SECTION - My reading list -->
        <div class="max-w-6xl px-4 py-10 sm:px-6 lg:px-8 lg:py-14 mx-auto">

            <div class="mb-6 max-w-2xl text-center mx-auto pb-3">
                <h1 class="font-medium text-black text-3xl sm:text-4xl dark:text-white">
                    My reading list
                </h1>
            </div>

            <div class="grid sm:grid-cols-2 lg:grid-cols-4 gap-4">

                <!-- LINK - #10 The Pivot Year  -->
                <a href="https://amzn.to/4m6z5QC" target="_blank" rel="noopener noreferrer" class="flex flex-col h-full transition duration-300 group border border-gray-200 hover:shadow-lg rounded-xl">
                    <!-- Cover -->
                    <div class="relative mt-5">
                        <img
                            class="max-w-[160px] max-h-48 w-full h-auto object-contain rounded-xl mx-auto"
                            src="/files/images/books/10-the-pivot-year.webp"
                            alt="The Pivot Year" />
                    </div>
                    <!-- Content -->
                    <div class="flex flex-col h-full p-5">
                        <div class="text-center">
                            <!-- Title -->
                            <h3 class="text-2xl font-semibold text-gray-800 dark:text-neutral-300 group-hover:text-blue-500 transition-colors duration-300">
                                The Pivot Year
                            </h3>
                            <!-- Data -->
                            <div class="flex justify-center items-center gap-2 mt-2 text-sm text-gray-500 dark:text-neutral-400">
                                <span>#10</span>
                                <span class="text-gray-500">•</span>
                                <span>10.01.2025</span>
                                <span class="text-gray-500">•</span>
                                <span class="flex items-center gap-x-1">
                                    <img src="/files/images/flags/en.svg" class="w-5 h-5" alt="English flag">
                                </span>
                                <span class="text-gray-500">•</span>
                                <?php include $_SERVER['DOCUMENT_ROOT'] . '/files/components/books/formats/audiobook.php'; ?>
                            </div>
                            <!-- Rating -->
                            <div class="flex justify-center gap-1 mt-2">
                                <?php include $_SERVER['DOCUMENT_ROOT'] . '/files/components/books/ratings/1.php'; ?>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="mt-3 text-left text-gray-600 dark:text-neutral-400 text-md">
                            A book that promises to help you become the person you truly want to be in 365 days.
                            While it didn’t fully resonate with me, I appreciate its purpose. It’s a great choice for those stepping into personal development for the first time. As for me, I think I’ve read to too many similar books already.
                        </div>

                        <!-- CTA -->
                        <div class="mt-3 flex justify-center">
                            <p class="inline-flex items-center gap-x-1 text-md font-semibold text-gray-600 group-hover:text-blue-500">
                                Get it here
                                <svg class="mt-1 shrink-0 size-4 transition ease-in-out group-hover:text-blue-500 group-hover:translate-x-1 group-focus:translate-x-1"
                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path d="m9 18 6-6-6-6" />
                                </svg>
                            </p>
                        </div>
                    </div>
                </a>

                <!-- LINK - #9 Imminent -->
                <a href="https://amzn.to/41Aj12C" target="_blank" rel="noopener noreferrer" class="flex flex-col h-full transition duration-300 group border border-gray-200 hover:shadow-lg rounded-xl">
                    <!-- Cover -->
                    <div class="relative mt-5">
                        <img
                            class="max-w-[160px] max-h-48 w-auto h-auto object-contain rounded-xl mx-auto"
                            src="/files/images/books/9-imminent.webp"
                            alt="Imminent" />
                    </div>
                    <!-- Content -->
                    <div class="flex flex-col h-full p-5">
                        <div class="text-center">
                            <!-- Title -->
                            <h3 class="text-2xl font-semibold text-gray-800 dark:text-neutral-300 group-hover:text-blue-500 transition-colors duration-300">
                                Imminent
                            </h3>
                            <!-- Data -->
                            <div class="flex justify-center items-center gap-2 mt-2 text-sm text-gray-500 dark:text-neutral-400">
                                <span>#9</span>
                                <span class="text-gray-500">•</span>
                                <span>13.12.2024</span>
                                <span class="text-gray-500">•</span>
                                <span class="flex items-center gap-x-1">
                                    <img src="/files/images/flags/en.svg" class="w-5 h-5" alt="English flag">
                                </span>
                                <span class="text-gray-500">•</span>
                                <?php include $_SERVER['DOCUMENT_ROOT'] . '/files/components/books/formats/audiobook.php'; ?>
                            </div>
                            <!-- Rating -->
                            <div class="flex justify-center gap-1 mt-2">
                                <?php include $_SERVER['DOCUMENT_ROOT'] . '/files/components/books/ratings/3.php'; ?>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="mt-3 text-left text-gray-600 dark:text-neutral-400 text-md">
                            A great audiobook makes time fly, and at times, this one did just that. An intriguing narrative that leaves the listener with the realization that we are not alone in this world. Inspiring how the author gave up his comfortable life to make people aware of this truth.
                        </div>

                        <!-- CTA -->
                        <div class="mt-3 flex justify-center">
                            <p class="inline-flex items-center gap-x-1 text-md font-semibold text-gray-600 group-hover:text-blue-500">
                                Get it here
                                <svg class="mt-1 shrink-0 size-4 transition ease-in-out group-hover:text-blue-500 group-hover:translate-x-1 group-focus:translate-x-1"
                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path d="m9 18 6-6-6-6" />
                                </svg>
                            </p>
                        </div>
                    </div>
                </a>

                <!-- LINK - #8 Casino Royale -->
                <a href="https://amzn.to/41nboMO" target="_blank" rel="noopener noreferrer" class="flex flex-col h-full transition duration-300 group border border-gray-200 hover:shadow-lg rounded-xl">
                    <!-- Cover -->
                    <div class="relative mt-5">
                        <img
                            class="max-w-[160px] max-h-48 w-auto h-auto object-contain rounded-xl mx-auto"
                            src="/files/images/books/8-casino-royale.webp"
                            alt="Casino Royale" />
                    </div>
                    <!-- Content -->
                    <div class="flex flex-col h-full p-5">
                        <div class="text-center">
                            <!-- Title -->
                            <h3 class="text-2xl font-semibold text-gray-800 dark:text-neutral-300 group-hover:text-blue-500 transition-colors duration-300">
                                Casino Royale
                            </h3>
                            <!-- Author -->
                            <h5 class="mt-1 text-md text-gray-700 dark:text-neutral-200">Ian Fleming</h5>
                            <!-- Data -->
                            <div class="flex justify-center items-center gap-2 mt-2 text-sm text-gray-500 dark:text-neutral-400">
                                <span>#8</span>
                                <span class="text-gray-500">•</span>
                                <span>27.11.2024</span>
                                <span class="text-gray-500">•</span>
                                <span class="flex items-center gap-x-1">
                                    <img src="/files/images/flags/en.svg" class="w-5 h-5" alt="English flag">
                                </span>
                                <span class="text-gray-500">•</span>
                                <?php include $_SERVER['DOCUMENT_ROOT'] . '/files/components/books/formats/audiobook.php'; ?>
                            </div>
                            <!-- Rating -->
                            <div class="flex justify-center gap-1 mt-2">
                                <?php include $_SERVER['DOCUMENT_ROOT'] . '/files/components/books/ratings/1.php'; ?>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="mt-3 text-left text-gray-600 dark:text-neutral-400 text-md">
                            I listened out of curiosity, as the movie is one of my favorites. While the book didn't live up to the movie, I'm still glad to have experienced it and learned what James Bond books are all about.
                        </div>

                        <!-- CTA -->
                        <div class="mt-3 flex justify-center">
                            <p class="inline-flex items-center gap-x-1 text-md font-semibold text-gray-600 group-hover:text-blue-500">
                                Get it here
                                <svg class="mt-1 shrink-0 size-4 transition ease-in-out group-hover:text-blue-500 group-hover:translate-x-1 group-focus:translate-x-1"
                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path d="m9 18 6-6-6-6" />
                                </svg>
                            </p>
                        </div>
                    </div>
                </a>

                <!-- LINK - #7 Micromastery -->
                <a href="https://amzn.to/41rPwQe" target="_blank" rel="noopener noreferrer" class="flex flex-col h-full transition duration-300 group border border-gray-200 hover:shadow-lg rounded-xl">
                    <!-- Cover -->
                    <div class="relative mt-5">
                        <img
                            class="max-w-[160px] max-h-48 w-auto h-auto object-contain rounded-xl mx-auto"
                            src="/files/images/books/7-micromastery.webp"
                            alt="Micromastery" />
                    </div>
                    <!-- Content -->
                    <div class="flex flex-col h-full p-5">
                        <div class="text-center">
                            <!-- Title -->
                            <h3 class="text-2xl font-semibold text-gray-800 dark:text-neutral-300 group-hover:text-blue-500 transition-colors duration-300">
                                Micromastery
                            </h3>
                            <!-- Author -->
                            <h5 class="mt-1 text-md text-gray-700 dark:text-neutral-200">Robert Twigger</h5>
                            <!-- Data -->
                            <div class="flex justify-center items-center gap-2 mt-2 text-sm text-gray-500 dark:text-neutral-400">
                                <span>#7</span>
                                <span class="text-gray-500">•</span>
                                <span>25.11.2024</span>
                                <span class="text-gray-500">•</span>
                                <span class="flex items-center gap-x-1">
                                    <img src="/files/images/flags/en.svg" class="w-5 h-5" alt="English flag">
                                </span>
                                <span class="text-gray-500">•</span>
                                <?php include $_SERVER['DOCUMENT_ROOT'] . '/files/components/books/formats/audiobook.php'; ?>
                            </div>
                            <!-- Rating -->
                            <div class="flex justify-center gap-1 mt-2">
                                <?php include $_SERVER['DOCUMENT_ROOT'] . '/files/components/books/ratings/1.php'; ?>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="mt-3 text-left text-gray-600 dark:text-neutral-400 text-md">
                            A practical book on how to quickly master useful skills. I loved the abundance of practical tips, and thanks to this book, I can now make the world's best omelet.
                        </div>

                        <!-- CTA -->
                        <div class="mt-3 flex justify-center">
                            <p class="inline-flex items-center gap-x-1 text-md font-semibold text-gray-600 group-hover:text-blue-500">
                                Get it here
                                <svg class="mt-1 shrink-0 size-4 transition ease-in-out group-hover:text-blue-500 group-hover:translate-x-1 group-focus:translate-x-1"
                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path d="m9 18 6-6-6-6" />
                                </svg>
                            </p>
                        </div>
                    </div>
                </a>

                <!-- LINK - #6 Be Useful -->
                <a href="https://amzn.to/3BlzGMC" target="_blank" rel="noopener noreferrer" class="flex flex-col h-full transition duration-300 group border border-gray-200 hover:shadow-lg rounded-xl">
                    <!-- Cover -->
                    <div class="relative mt-5">
                        <img
                            class="max-w-[160px] max-h-48 w-auto h-auto object-contain rounded-xl mx-auto"
                            src="/files/images/books/6-be-useful.webp"
                            alt="Be Useful" />
                    </div>
                    <!-- Content -->
                    <div class="flex flex-col h-full p-5">
                        <div class="text-center">
                            <!-- Title -->
                            <h3 class="text-2xl font-semibold text-gray-800 dark:text-neutral-300 group-hover:text-blue-500 transition-colors duration-300">
                                Be Useful
                            </h3>
                            <!-- Author -->
                            <h5 class="mt-1 text-md text-gray-700 dark:text-neutral-200">Arnold Schwarzenegger</h5>
                            <!-- Data -->
                            <div class="flex justify-center items-center gap-2 mt-2 text-sm text-gray-500 dark:text-neutral-400">
                                <span>#6</span>
                                <span class="text-gray-500">•</span>
                                <span>21.11.2024</span>
                                <span class="text-gray-500">•</span>
                                <span class="flex items-center gap-x-1">
                                    <img src="/files/images/flags/en.svg" class="w-5 h-5" alt="English flag">
                                </span>
                                <span class="text-gray-500">•</span>
                                <?php include $_SERVER['DOCUMENT_ROOT'] . '/files/components/books/formats/audiobook.php'; ?>
                            </div>
                            <!-- Rating -->
                            <div class="flex justify-center gap-1 mt-2">
                                <?php include $_SERVER['DOCUMENT_ROOT'] . '/files/components/books/ratings/2.php'; ?>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="mt-3 text-left text-gray-600 dark:text-neutral-400 text-md">
                            Packed with inspiring advice for achieving success, made even better by Arnold narrating it himself. A surprisingly enjoyable and motivating listen.
                        </div>

                        <!-- CTA -->
                        <div class="mt-3 flex justify-center">
                            <p class="inline-flex items-center gap-x-1 text-md font-semibold text-gray-600 group-hover:text-blue-500">
                                Get it here
                                <svg class="mt-1 shrink-0 size-4 transition ease-in-out group-hover:text-blue-500 group-hover:translate-x-1 group-focus:translate-x-1"
                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path d="m9 18 6-6-6-6" />
                                </svg>
                            </p>
                        </div>
                    </div>
                </a>

                <!-- LINK - #5 How Luck Happens -->
                <a href="https://amzn.to/3ZsLcO5" target="_blank" rel="noopener noreferrer" class="flex flex-col h-full transition duration-300 group border border-gray-200 hover:shadow-lg rounded-xl">
                    <!-- Cover -->
                    <div class="relative mt-5">
                        <img
                            class="max-w-[160px] max-h-48 w-auto h-auto object-contain rounded-xl mx-auto"
                            src="/files/images/books/5-how-luck-happens.webp"
                            alt="How Luck Happens" />
                    </div>
                    <!-- Content -->
                    <div class="flex flex-col h-full p-5">
                        <div class="text-center">
                            <!-- Title -->
                            <h3 class="text-2xl font-semibold text-gray-800 dark:text-neutral-300 group-hover:text-blue-500 transition-colors duration-300">
                                How Luck Happens
                            </h3>
                            <!-- Author -->
                            <h5 class="mt-1 text-md text-gray-700 dark:text-neutral-200">Janice Kaplan</h5>
                            <!-- Data -->
                            <div class="flex justify-center items-center gap-2 mt-2 text-sm text-gray-500 dark:text-neutral-400">
                                <span>#5</span>
                                <span class="text-gray-500">•</span>
                                <span>17.11.2024</span>
                                <span class="text-gray-500">•</span>
                                <span class="flex items-center gap-x-1">
                                    <img src="/files/images/flags/en.svg" class="w-5 h-5" alt="English flag">
                                </span>
                                <span class="text-gray-500">•</span>
                                <?php include $_SERVER['DOCUMENT_ROOT'] . '/files/components/books/formats/audiobook.php'; ?>
                            </div>
                            <!-- Rating -->
                            <div class="flex justify-center gap-1 mt-2">
                                <?php include $_SERVER['DOCUMENT_ROOT'] . '/files/components/books/ratings/1.php'; ?>
                            </div>
                        </div>

                        <!-- Description - left-aligned -->
                        <div class="mt-3 text-left text-gray-600 dark:text-neutral-400 text-md">
                            Luck is a combination of talent, hard work, and random chance. While it wasn't my favorite book, it inspired me to see opportunities and take action.
                        </div>

                        <!-- CTA - center aligned -->
                        <div class="mt-3 flex justify-center">
                            <p class="inline-flex items-center gap-x-1 text-md font-semibold text-gray-600 group-hover:text-blue-500">
                                Get it here
                                <svg class="mt-1 shrink-0 size-4 transition ease-in-out group-hover:text-blue-500 group-hover:translate-x-1 group-focus:translate-x-1"
                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path d="m9 18 6-6-6-6" />
                                </svg>
                            </p>
                        </div>
                    </div>
                </a>

                <!-- LINK - #4 Patriot -->
                <a href="https://amzn.to/3VAMtBs" target="_blank" rel="noopener noreferrer" class="flex flex-col h-full transition duration-300 group border border-gray-200 hover:shadow-lg rounded-xl">
                    <!-- Cover -->
                    <div class="relative mt-5">
                        <img
                            class="max-w-[160px] max-h-48 w-auto h-auto object-contain rounded-xl mx-auto"
                            src="/files/images/books/4-patriot.webp"
                            alt="Patriot" />
                    </div>
                    <!-- Content -->
                    <div class="flex flex-col h-full p-5">
                        <div class="text-center">
                            <!-- Title -->
                            <h3 class="text-2xl font-semibold text-gray-800 dark:text-neutral-300 group-hover:text-blue-500 transition-colors duration-300">
                                Patriot
                            </h3>
                            <!-- Author -->
                            <h5 class="mt-1 text-md text-gray-700 dark:text-neutral-200">Alexei Navalny</h5>
                            <!-- Data -->
                            <div class="flex justify-center items-center gap-2 mt-2 text-sm text-gray-500 dark:text-neutral-400">
                                <span>#4</span>
                                <span class="text-gray-500">•</span>
                                <span>13.11.2024</span>
                                <span class="text-gray-500">•</span>
                                <span class="flex items-center gap-x-1">
                                    <img src="/files/images/flags/en.svg" class="w-5 h-5" alt="English flag">
                                </span>
                                <span class="text-gray-500">•</span>
                                <?php include $_SERVER['DOCUMENT_ROOT'] . '/files/components/books/formats/audiobook.php'; ?>
                            </div>
                            <!-- Rating -->
                            <div class="flex justify-center gap-1 mt-2">
                                <?php include $_SERVER['DOCUMENT_ROOT'] . '/files/components/books/ratings/2.php'; ?>
                            </div>
                        </div>

                        <!-- Description - left-aligned -->
                        <div class="mt-3 text-left text-gray-600 dark:text-neutral-400 text-md">
                            I've always been interested in the history of the United States, and this book provided a great overview of the country's development from its founding to the present day.
                        </div>

                        <!-- CTA - center aligned -->
                        <div class="mt-3 flex justify-center">
                            <p class="inline-flex items-center gap-x-1 text-md font-semibold text-gray-600 group-hover:text-blue-500">
                                Get it here
                                <svg class="mt-1 shrink-0 size-4 transition ease-in-out group-hover:text-blue-500 group-hover:translate-x-1 group-focus:translate-x-1"
                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path d="m9 18 6-6-6-6" />
                                </svg>
                            </p>
                        </div>
                    </div>
                </a>

                <!-- LINK - #3 Simply Artificial Intelligence -->
                <a href="https://amzn.to/3BvZ81O" target="_blank" rel="noopener noreferrer" class="flex flex-col h-full transition duration-300 group border border-gray-200 hover:shadow-lg rounded-xl">
                    <!-- Cover -->
                    <div class="relative mt-5">
                        <img
                            class="max-w-[160px] max-h-48 w-auto h-auto object-contain rounded-xl mx-auto"
                            src="/files/images/books/3-simply-artificial-intelligence.webp"
                            alt="Simply Artificial Intelligence" />
                    </div>
                    <!-- Content -->
                    <div class="flex flex-col h-full p-5">
                        <div class="text-center">
                            <!-- Title -->
                            <h3 class="text-2xl font-semibold text-gray-800 dark:text-neutral-300 group-hover:text-blue-500 transition-colors duration-300">
                                Simply Artificial Intelligence
                            </h3>
                            <!-- Author -->
                            <h5 class="mt-1 text-md text-gray-700 dark:text-neutral-200">DK</h5>
                            <!-- Data -->
                            <div class="flex justify-center items-center gap-2 mt-2 text-sm text-gray-500 dark:text-neutral-400">
                                <span>#3</span>
                                <span class="text-gray-500">•</span>
                                <span>07.06.2024</span>
                                <span class="text-gray-500">•</span>
                                <span class="flex items-center gap-x-1">
                                    <img src="/files/images/flags/en.svg" class="w-5 h-5" alt="English flag">
                                </span>
                                <span class="text-gray-500">•</span>
                                <?php include $_SERVER['DOCUMENT_ROOT'] . '/files/components/books/formats/audiobook.php'; ?>
                            </div>
                            <!-- Rating -->
                            <div class="flex justify-center gap-1 mt-2">
                                <?php include $_SERVER['DOCUMENT_ROOT'] . '/files/components/books/ratings/3.php'; ?>
                            </div>
                        </div>

                        <!-- Description - left-aligned -->
                        <div class="mt-3 text-left text-gray-600 dark:text-neutral-400 text-md">
                            A basic introduction to AI for beginners. Since I've read and listened to several books on the topic, it lacked depth and novelty for me.
                        </div>

                        <!-- CTA - center aligned -->
                        <div class="mt-3 flex justify-center">
                            <p class="inline-flex items-center gap-x-1 text-md font-semibold text-gray-600 group-hover:text-blue-500">
                                Get it here
                                <svg class="mt-1 shrink-0 size-4 transition ease-in-out group-hover:text-blue-500 group-hover:translate-x-1 group-focus:translate-x-1"
                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path d="m9 18 6-6-6-6" />
                                </svg>
                            </p>
                        </div>
                    </div>
                </a>

                <!-- LINK - #2 The ChatGPT Millionaire -->
                <a href="https://amzn.to/3ZMeyIA" target="_blank" rel="noopener noreferrer" class="flex flex-col h-full transition duration-300 group border border-gray-200 hover:shadow-lg rounded-xl">
                    <!-- Cover -->
                    <div class="relative mt-5">
                        <img
                            class="max-w-[160px] max-h-48 w-auto h-auto object-contain rounded-xl mx-auto"
                            src="/files/images/books/2-the-chatgpt-millionaire.webp"
                            alt="The ChatGPT Millionaire" />
                    </div>
                    <!-- Content -->
                    <div class="flex flex-col h-full p-5">
                        <div class="text-center">
                            <!-- Title -->
                            <h3 class="text-2xl font-semibold text-gray-800 dark:text-neutral-300 group-hover:text-blue-500 transition-colors duration-300">
                                The ChatGPT Millionaire
                            </h3>
                            <!-- Author -->
                            <h5 class="mt-1 text-md text-gray-700 dark:text-neutral-200">Neil Dagger</h5>
                            <!-- Data -->
                            <div class="flex justify-center items-center gap-2 mt-2 text-sm text-gray-500 dark:text-neutral-400">
                                <span>#2</span>
                                <span class="text-gray-500">•</span>
                                <span>07.06.2024</span>
                                <span class="text-gray-500">•</span>
                                <span class="flex items-center gap-x-1">
                                    <img src="/files/images/flags/en.svg" class="w-5 h-5" alt="English flag">
                                </span>
                                <span class="text-gray-500">•</span>
                                <?php include $_SERVER['DOCUMENT_ROOT'] . '/files/components/books/formats/audiobook.php'; ?>
                            </div>
                            <!-- Rating -->
                            <div class="flex justify-center gap-1 mt-2">
                                <?php include $_SERVER['DOCUMENT_ROOT'] . '/files/components/books/ratings/3.php'; ?>
                            </div>
                        </div>

                        <!-- Description - left-aligned -->
                        <div class="mt-3 text-left text-gray-600 dark:text-neutral-400 text-md">
                            Great title that promises a lot but, unfortunately, delivers little value or new ideas. I wouldn't be surprised if the book itself was written by ChatGPT.
                        </div>

                        <!-- CTA - center aligned -->
                        <div class="mt-3 flex justify-center">
                            <p class="inline-flex items-center gap-x-1 text-md font-semibold text-gray-600 group-hover:text-blue-500">
                                Get it here
                                <svg class="mt-1 shrink-0 size-4 transition ease-in-out group-hover:text-blue-500 group-hover:translate-x-1 group-focus:translate-x-1"
                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path d="m9 18 6-6-6-6" />
                                </svg>
                            </p>
                        </div>
                    </div>
                </a>

                <!-- LINK - #1 Welcome to AI -->
                <a href="https://amzn.to/3ZPnux7" target="_blank" rel="noopener noreferrer" class="flex flex-col h-full transition duration-300 group border border-gray-200 hover:shadow-lg rounded-xl">
                    <!-- Cover -->
                    <div class="relative mt-5">
                        <img
                            class="max-w-[160px] max-h-48 w-auto h-auto object-contain rounded-xl mx-auto"
                            src="/files/images/books/1-welcome-to-ai.webp"
                            alt="Welcome to AI" />
                    </div>
                    <!-- Content -->
                    <div class="flex flex-col h-full p-5">
                        <div class="text-center">
                            <!-- Title -->
                            <h3 class="text-2xl font-semibold text-gray-800 dark:text-neutral-300 group-hover:text-blue-500 transition-colors duration-300">
                                Welcome to AI
                            </h3>
                            <!-- Author -->
                            <h5 class="mt-1 text-md text-gray-700 dark:text-neutral-200">David L. Shrier</h5>
                            <!-- Data -->
                            <div class="flex justify-center items-center gap-2 mt-2 text-sm text-gray-500 dark:text-neutral-400">
                                <span>#1</span>
                                <span class="text-gray-500">•</span>
                                <span>01.06.2024</span>
                                <span class="text-gray-500">•</span>
                                <span class="flex items-center gap-x-1">
                                    <img src="/files/images/flags/en.svg" class="w-5 h-5" alt="English flag">
                                </span>
                                <span class="text-gray-500">•</span>
                                <?php include $_SERVER['DOCUMENT_ROOT'] . '/files/components/books/formats/audiobook.php'; ?>
                            </div>
                            <!-- Rating -->
                            <div class="flex justify-center gap-1 mt-2">
                                <?php include $_SERVER['DOCUMENT_ROOT'] . '/files/components/books/ratings/3.php'; ?>
                            </div>
                        </div>

                        <!-- Description - left-aligned -->
                        <div class="mt-3 text-left text-gray-600 dark:text-neutral-400 text-md">
                            A solid overview of AI history with some interesting insights. Sadly, it didn't provide much new information for me.
                        </div>

                        <!-- CTA - center aligned -->
                        <div class="mt-3 flex justify-center">
                            <p class="inline-flex items-center gap-x-1 text-md font-semibold text-gray-600 group-hover:text-blue-500">
                                Get it here
                                <svg class="mt-1 shrink-0 size-4 transition ease-in-out group-hover:text-blue-500 group-hover:translate-x-1 group-focus:translate-x-1"
                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path d="m9 18 6-6-6-6" />
                                </svg>
                            </p>
                        </div>
                    </div>
                </a>

            </div>

        </div>
        <!-- !SECTION - My reading list -->

    </main>
    <!-- ========== END MAIN CONTENT ========== -->


    <script>
        document.addEventListener("DOMContentLoaded", () => {
            const container = document.querySelector(".text-animation-container");
            const textA = document.getElementById("textA");
            const textB = document.getElementById("textB");

            const texts = ["reading 📖", "writing 📝", "creating 🎨"];
            const colors = ["#9983FF", "#FFB366", "#99E699"];

            let currentIndex = 0;
            let isATop = true;

            container.classList.add("loaded");

            // Set initial color
            textA.style.color = colors[currentIndex];

            const updateText = () => {
                const nextIndex = (currentIndex + 1) % texts.length;
                const nextColor = colors[nextIndex];

                const currentText = isATop ? textA : textB;
                const nextText = isATop ? textB : textA;

                // Prepare next text
                nextText.textContent = texts[nextIndex];
                nextText.style.color = nextColor;
                nextText.className = "animated-text animate-in";

                // Animate current out
                currentText.className = "animated-text animate-out";

                // After animation, reset and swap
                setTimeout(() => {
                    currentText.className = "animated-text hidden-below";
                    currentIndex = nextIndex;
                    isATop = !isATop;
                }, 2000); // match transition duration

                setTimeout(updateText, 4000); // next cycle in 4s
            };

            setTimeout(updateText, 4000);
        });
    </script>

</body>

</html>