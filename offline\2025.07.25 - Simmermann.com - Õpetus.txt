npx tailwindcss -c ./tailwind.config.js -i ./input.css -o ./dist/output.css --watch

cd C:\Users\<USER>\Desktop\simmermann.com
php -S localhost:8000

npm run build	Development build – käivitab watch mode’i, mis jälgib muudatusi ja uuendab CSS-i automaatselt. Ei tee minify’t.
npm run build:prod	Production build – teeb ühe korra kompileerimise ja minimeerib CSS-i väiksemaks ja kiiremaks. Ei jälgi muudatusi.