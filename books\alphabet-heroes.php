<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alphabet Heroes · Simmermann.com</title>
    <link href="../dist/output.css" rel="stylesheet">
    <link rel="icon" type="image/x-icon" href="../favicon.ico">

    <!-- Primary Meta Tags -->
    <meta name="title" content="Alphabet Heroes · Simmermann.com">
    <meta name="description" content="Magical bedtime stories that teach your child life's key values through charming characters and their enchanting adventures.">

    <!-- Facebook -->
    <meta property="og:type" content="book">
    <meta property="og:url" content="https://simmermann.com/books/alphabet-heroes.php">
    <meta property="og:title" content="Alphabet Heroes · Simmermann.com">
    <meta property="og:description" content="Discover 26 bedtime stories that teach children values like kindness, bravery, and curiosity through charming characters.">
    <meta property="og:image" content="https://simmermann.com/files/images/mybooks/alphabet-heroes.webp">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Alphabet Heroes · Simmermann.com">
    <meta name="twitter:description" content="Alphabet Heroes is a magical collection of ABC bedtime stories that teach essential values through delightful characters and adventures.">
    <meta name="twitter:image" content="https://simmermann.com/files/images/mybooks/alphabet-heroes.webp">

    <!-- Schema.org Structured Data -->
    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "Book",
            "name": "Alphabet Heroes",
            "author": {
                "@type": "Person",
                "name": "Martin Simmermann"
            },
            "datePublished": "2024-03-20",
            "bookFormat": "https://schema.org/EBook",
            "inLanguage": "en",
            "publisher": {
                "@type": "Organization",
                "name": "Martin Simmermann"
            },
            "offers": {
                "@type": "Offer",
                "url": "https://a.co/d/jfUNiH5",
                "price": "2.99",
                "priceCurrency": "USD",
                "availability": "https://schema.org/InStock"
            },
            "description": "Magical bedtime stories that teach your child life's key values through charming characters and their enchanting adventures.",
            "image": "https://simmermann.com/files/images/mybooks/alphabet-heroes.webp"
        }
    </script>
</head>

<body class="bg-white">
    <!-- Breadcrumbs -->
    <nav class="text-sm text-gray-500 mt-10 pt-2 text-center">
        <ol class=" items-center">
            <li class="inline-flex items-center">
                <a class="flex items-center text-md text-gray-500 hover:text-blue-600  focus:text-blue-600" href="../index.php">
                    Home
                </a>
                <svg class="shrink-0 mx-2 size-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="m9 18 6-6-6-6"></path>
                </svg>
            </li>
            <li class="inline-flex items-center text-md font-semibold text-gray-800 truncate" aria-current="page">
                Alphabet Heroes
            </li>
        </ol>
    </nav>

    <!-- LINK - Title -->
    <div class="text-center space-y-4 md:space-y-6 lg:pt-5 mt-5">
        <h1 class="text-4xl font-semibold text-gray-700 sm:text-5xl lg:text-6xl">
            Alphabet Heroes
        </h1>
    </div>

        <!-- Slider -->
        <div class="py-4 w-full max-w-[85rem] px-4 sm:px-6 lg:px-8 mx-auto">
      <div data-hs-carousel='{
          "loadingClasses": "opacity-0",
          "isInfiniteLoop": true,
          "dotsItemClasses": "hs-carousel-active:bg-white bg-white/50 size-3 rounded-full cursor-pointer"
        }' class="relative">
        <div class="hs-carousel relative overflow-hidden h-120 lg:h-160 w-full bg-gray-100 rounded-xl dark:bg-neutral-800">
          <div class="hs-carousel-body absolute top-0 bottom-0 start-0 flex flex-nowrap transition-transform duration-700 opacity-0">
            <!-- Item -->
            <div class="hs-carousel-slide">
              <!-- Slide -->
              <a class="h-120 lg:h-160 relative block rounded-xl focus:outline-hidden" href="#">
                <img class="absolute inset-0 size-full object-cover rounded-xl" src="https://images.unsplash.com/photo-1713492664635-1363f44734ef?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Hero Image">

                <div class="relative z-10 text-center size-full max-w-lg mx-auto px-12 flex flex-col justify-center items-center">
                  <p class="text-sm md:text-base uppercase text-white">
                    iPad cases
                  </p>

                  <h2 class="mt-2 font-semibold text-3xl sm:text-4xl lg:text-5xl text-white">
                    Including the new Pros and Airs.
                  </h2>

                  <div class="mt-7">
                    <span class="py-2 px-3 font-semibold text-sm bg-white text-gray-800 rounded-full">
                      Shop now
                    </span>
                  </div>
                </div>
              </a>
              <!-- End Slide -->
            </div>
            <!-- End Item -->

            <!-- Item -->
            <div class="hs-carousel-slide">
              <!-- Slide -->
              <a class="h-120 lg:h-160 relative block rounded-xl focus:outline-hidden" href="#">
                <img class="absolute inset-0 size-full object-cover rounded-xl" src="https://images.unsplash.com/photo-1644462982538-ee5ce6abd2db?q=80&w=1000&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Hero Image">

                <div class="relative z-10 size-full max-w-lg p-8 sm:p-16 flex flex-col">
                  <h2 class="font-semibold text-3xl sm:text-4xl lg:text-5xl text-gray-800">
                    New styles
                  </h2>

                  <p class="mt-3 text-sm md:text-base text-gray-800">
                    From lightweight leathers to the cases, new seasonal favorites are here.
                  </p>

                  <div class="mt-7">
                    <span class="py-2 px-3 font-semibold text-sm bg-white text-gray-800 rounded-full">
                      Shop now
                    </span>
                  </div>
                </div>
              </a>
              <!-- End Slide -->
            </div>
            <!-- End Item -->

            <!-- Item -->
            <div class="hs-carousel-slide">
              <!-- Slide -->
              <a class="h-120 lg:h-160 relative block overflow-hidden bg-linear-to-br from-emerald-500 to-emerald-900 rounded-xl focus:outline-hidden dark:bg-neutral-800" href="../../pro/shop/listing.html">
                <!-- Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 items-center gap-5">
                  <!-- Content -->
                  <div class="p-12 sm:p-16 md:ps-20 md:pe-0 max-w-xl">
                    <span class="block font-bold uppercase text-2xl sm:text-3xl lg:text-4xl text-white">
                      Up to
                    </span>
                    <span class="block font-bold uppercase text-5xl sm:text-6xl lg:text-7xl xl:text-8xl text-white">
                      40% off
                    </span>
                    <span class="block md:text-end font-bold uppercase text-xl sm:text-2xl lg:text-3xl text-yellow-400">
                      For everything
                    </span>

                    <div class="mt-10 md:mt-20">
                      <h2 class="font-semibold text-2xl md:text-3xl text-white">
                        End of year sale
                      </h2>

                      <p class="mt-1 text-white">
                        Celebrate with up to 40% off everything for a limited time.
                      </p>

                      <div class="mt-3 md:mt-5">
                        <span class="py-2 px-3 font-semibold text-sm bg-white text-gray-800 rounded-full">
                          Shop now
                        </span>
                      </div>
                    </div>
                  </div>
                  <!-- End Content -->

                  <!-- Images -->
                  <div class="h-120 lg:h-160 grid grid-cols-2 gap-3 sm:gap-5 -rotate-12">
                    <div class="flex flex-col gap-3 sm:gap-5">
                      <div class="p-1.5 bg-white rounded-2xl lg:rounded-3xl shadow-2xl dark:bg-neutral-900">
                        <img class="size-full object-cover rounded-xl lg:rounded-2xl" src="https://images.unsplash.com/photo-1548874468-025d0edfdf8b?q=80&w=320&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Product Image">
                      </div>
                      <div class="p-1.5 bg-white rounded-2xl lg:rounded-3xl shadow-2xl dark:bg-neutral-900">
                        <img class="size-full object-cover rounded-xl lg:rounded-2xl" src="https://images.unsplash.com/photo-1513652990199-8a52e2313122?q=80&w=320&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Product Image">
                      </div>
                    </div>

                    <div class="flex flex-col gap-3 sm:gap-5 -mt-6">
                      <div class="p-1.5 bg-white rounded-2xl lg:rounded-3xl shadow-2xl dark:bg-neutral-900">
                        <img class="size-full object-cover rounded-xl lg:rounded-2xl" src="https://images.unsplash.com/photo-1652540492984-c347f10fcbaf?q=80&w=320&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Product Image">
                      </div>
                      <div class="p-1.5 bg-white rounded-2xl lg:rounded-3xl shadow-2xl dark:bg-neutral-900">
                        <img class="size-full object-cover rounded-xl lg:rounded-2xl" src="https://images.unsplash.com/photo-1525547719571-a2d4ac8945e2?q=80&w=320&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Product Image">
                      </div>
                    </div>
                  </div>
                  <!-- End Images -->
                </div>
                <!-- End Grid -->
              </a>
              <!-- End Slide -->
            </div>
            <!-- End Item -->
          </div>
        </div>

        <button type="button" class="hs-carousel-prev hs-carousel-disabled:opacity-50 hs-carousel-disabled:cursor-default absolute top-1/2 start-1 sm:start-4 inline-flex justify-center items-center size-10 bg-white border border-gray-100 text-gray-800 rounded-full shadow-2xs hover:bg-gray-100 -translate-y-1/2 focus:outline-hidden">
          <span class="text-2xl" aria-hidden="true">
            <svg class="shrink-0 size-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="m15 18-6-6 6-6" />
            </svg>
          </span>
          <span class="sr-only">Previous</span>
        </button>
        <button type="button" class="hs-carousel-next hs-carousel-disabled:opacity-50 hs-carousel-disabled:cursor-default absolute top-1/2 end-1 sm:end-4 inline-flex justify-center items-center size-10 bg-white border border-gray-100 text-gray-800 rounded-full shadow-2xs hover:bg-gray-100 -translate-y-1/2 focus:outline-hidden">
          <span class="sr-only">Next</span>
          <span class="text-2xl" aria-hidden="true">
            <svg class="shrink-0 size-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="m9 18 6-6-6-6" />
            </svg>
          </span>
        </button>

        <div class="hs-carousel-pagination flex justify-center absolute bottom-4 start-0 end-0 space-x-2"></div>
      </div>
    </div>
    <!-- End Slider -->

    <div class="max-w-7xl px-4 pt-10 pb-10 sm:px-6 lg:px-8 lg:pt-12 lg:pb-14 mx-auto mb-10">
        <div class="grid lg:grid-cols-2 lg:gap-x-16 lg:items-start gap-6">
            <div class="relative">
                <img class="relative w-full rounded-xl" src="../files/images/mybooks/alphabet-heroes.webp" alt="Alphabet Heroes" loading="lazy">
            </div>

            <div>
                <div class="mt-8 lg:mt-0">
                    <div class="space-y-4">
                        <!-- LINK - Data -->
                        <div class="grid grid-cols-2 lg:grid-cols-3 gap-4 p-6 bg-white rounded-xl">
                            <div>
                                <p><span class="text-gray-700 font-semibold text-lg">Author:</span><br><span class="text-gray-700 text-md">Martin Simmermann</span></p>
                            </div>
                            <div>
                                <p><span class="text-gray-700 font-semibold text-lg">Published:</span><br><span class="text-gray-600 text-md">20 March, 2024</span></p>
                            </div>
                            <div>
                                <p><span class="text-gray-700 font-semibold text-lg">Length:</span><br><span class="text-gray-700 text-md">56 pages</span></p>
                            </div>
                            <div>
                                <p><span class="text-gray-700 font-semibold text-lg">Format:</span><br><span class="text-gray-700 text-md">Kindle e-book</span></p>
                            </div>
                            <div>
                                <p><span class="text-gray-700 font-semibold text-lg">Language:</span><br><span class="text-gray-700 text-md">English</span></p>
                            </div>
                            <div>
                                <p><span class="text-gray-700 font-semibold text-lg">Price:</span><br><span class="text-gray-700 text-md">$2.99</span></p>
                            </div>
                        </div>

                        <!-- LINK - Introduction -->
                        <div class="space-y-4 p-6 pt-0 bg-white rounded-xl">
                            <h3 class="text-2xl font-semibold text-gray-700">Introduction</h3>
                            <div class="space-y-4 text-gray-700 leading-relaxed text-lg">
                                <p>
                                    Alphabet Heroes is a heartwarming collection of bedtime stories designed to teach important
                                    life lessons and values through charming characters and their enchanting adventures. Each letter of
                                    the alphabet introduces a unique character and story that highlights a specific value, such as
                                    bravery, kindness, curiosity, and determination.
                                </p>
                                <p>
                                    These stories are crafted not only to teach the ABCs but also to help shape a child's understanding of
                                    the world around them. Perfect for young readers and parents alike, Alphabet Heroes is a
                                    versatile book that can be enjoyed in many ways.
                                </p>
                                <p>
                                    For younger children, the delightful illustrations bring the characters to life, while older children
                                    can learn letters and discover the deeper meanings in each story.
                                </p>
                            </div>
                        </div>
                        <!-- LINK - Button -->
                        <a href="https://amzn.to/46MXh6j" target="_blank" rel="noopener noreferrer" class="w-full py-3 px-4 inline-flex justify-center items-center gap-x-2 text-lg font-medium rounded-xl border border-transparent bg-gray-900 text-white hover:bg-gray-600 focus:outline-hidden focus:bg-gray-600 disabled:opacity-50 disabled:pointer-events-none">
                            Buy on Amazon
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

  <!-- JS PLUGINS -->
  <script src="https://cdn.jsdelivr.net/npm/preline@2.0.3/dist/preline.min.js"></script>


</body>

</html>